+--- androidx.core:core-ktx:1.13.1 (*)
+--- com.google.protobuf:protobuf-javalite:4.28.1
+--- project :iconloaderlib
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|    +--- androidx.core:core-ktx:1.13.1 (*)
|    \--- androidx.palette:palette-ktx:1.0.0
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.2.50 -> 2.0.20 (*)
|         \--- androidx.palette:palette:1.0.0
|              +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|              \--- androidx.legacy:legacy-support-core-utils:1.0.0
|                   +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|                   +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|                   +--- androidx.documentfile:documentfile:1.0.0
|                   |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|                   +--- androidx.loader:loader:1.0.0
|                   |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|                   |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|                   |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.5
|                   |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|                   |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|                   |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.5 (*)
|                   |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.5
|                   |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.5 (*)
|                   |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.20 (*)
|                   |    |    |    \--- androidx.lifecycle:lifecycle-common:2.8.5 (c)
|                   |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.20 (*)
|                   |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|                   |    |    \--- androidx.lifecycle:lifecycle-common:2.8.5 (c)
|                   |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.5 (*)
|                   +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|                   |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|                   \--- androidx.print:print:1.0.0
|                        \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
+--- project :searchuilib
|    \--- androidx.core:core-ktx:1.13.1 (*)
+--- project :animationlib
|    +--- androidx.core:core-ktx:1.13.1 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.21
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.21 -> 2.0.20 (*)
|    +--- androidx.core:core-animation:1.0.0-rc01 -> 1.0.0
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|    |    +--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    |    +--- androidx.core:core:1.13.0 -> 1.13.1 (*)
|    |    \--- androidx.tracing:tracing:1.0.0 (*)
|    \--- androidx.core:core-ktx:1.12.0 -> 1.13.1 (*)
+--- androidx.profileinstaller:profileinstaller:1.3.1 (*)
+--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    +--- androidx.collection:collection:1.0.0 -> 1.4.2 (*)
|    \--- androidx.legacy:legacy-support-core-utils:1.0.0 (*)
+--- androidx.recyclerview:recyclerview:1.3.2
|    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    +--- androidx.collection:collection:1.0.0 -> 1.4.2 (*)
|    +--- androidx.core:core:1.7.0 -> 1.13.1 (*)
|    +--- androidx.customview:customview:1.0.0 -> 1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    +--- androidx.core:core:1.3.0 -> 1.13.1 (*)
|    |    \--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    +--- androidx.customview:customview-poolingcontainer:1.0.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.1.0-beta02 (c)
+--- com.applovin.mediation:bytedance-adapter:6.3.0.4.0
|    \--- com.applovin:applovin-sdk:[13.0.0,) -> 13.0.1 (*)
+--- com.applovin.mediation:unityads-adapter:4.12.4.0
|    +--- com.applovin:applovin-sdk:[13.0.0,) -> 13.0.1 (*)
|    \--- com.unity3d.ads:unity-ads:4.12.4
|         +--- androidx.activity:activity-ktx:1.7.1 -> 1.9.2 (*)
|         +--- androidx.core:core-ktx:1.9.0 -> 1.13.1 (*)
|         +--- androidx.lifecycle:lifecycle-process:2.6.1 -> 2.8.5 (*)
|         +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 -> 2.8.5 (*)
|         +--- androidx.startup:startup-runtime:1.1.1 (*)
|         +--- androidx.webkit:webkit:1.6.1 -> 1.11.0-alpha02 (*)
|         +--- androidx.datastore:datastore:1.0.0 -> 1.1.1 (*)
|         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 -> 1.9.0 (*)
|         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10 -> 1.9.10 (*)
|         +--- com.squareup.okhttp3:okhttp:3.12.13 -> 4.12.0 (*)
|         +--- com.google.protobuf:protobuf-kotlin-lite:3.21.12
|         |    +--- com.google.protobuf:protobuf-javalite:3.21.12 -> 4.28.1
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.6.0 -> 2.0.20 (*)
|         +--- androidx.work:work-runtime-ktx:2.7.0 -> 2.9.1 (*)
|         \--- com.google.android.gms:play-services-cronet:18.0.1
|              +--- com.google.android.gms:play-services-base:18.0.1 -> 18.3.0 (*)
|              +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|              +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|              \--- org.chromium.net:cronet-api:72.3626.96
+--- com.thirdparty.android:kwai-api:1.2.13
+--- com.thirdparty.android:kwai-impl:1.2.13
+--- com.google.android.material:material:1.12.0 (*)
+--- androidx.media3:media3-exoplayer:1.3.1 (*)
+--- com.thirdparty.android:apicmob:5.4.2.6
+--- com.github.megatronking.stringfog:xor:4.0.1 (*)
+--- com.android.iaa:basement:7.19.0-beta
|    +--- com.google.firebase:firebase-analytics:20.1.2 (*)
|    +--- com.squareup.okhttp3:okhttp:3.12.3 -> 4.12.0 (*)
|    +--- com.squareup.okio:okio:2.4.0 -> 3.9.0 (*)
|    +--- com.adjust.sdk:adjust-android:4.38.5
|    +--- com.publish.bridge:unity:19.1.0-beta
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.20 -> 1.9.10 (*)
|    |    +--- androidx.annotation:annotation:1.5.0 -> 1.8.1 (*)
|    |    \--- com.github.megatronking.stringfog:xor:4.0.1 (*)
|    +--- com.dynamic.android:dynamic-api:3.0.1-rename-package-beta (*)
|    +--- androidx.room:room-ktx:2.6.1 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.20 -> 1.9.10 (*)
|    +--- androidx.appcompat:appcompat:1.6.1 -> 1.7.0 (*)
|    +--- androidx.multidex:multidex:2.0.1
|    +--- com.android.installreferrer:installreferrer:2.2
|    +--- com.google.android.gms:play-services-ads-identifier:18.0.1 (*)
|    +--- com.google.android.ump:user-messaging-platform:2.1.0 -> 3.0.0 (*)
|    +--- androidx.work:work-runtime:2.9.1 (*)
|    +--- com.android.attr:attr-sdk:1.2.1-rename-package-beta
|    |    +--- com.squareup.okhttp3:okhttp:3.12.3 -> 4.12.0 (*)
|    |    +--- com.squareup.okio:okio:2.4.0 -> 3.9.0 (*)
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.20 -> 1.9.10 (*)
|    |    +--- androidx.appcompat:appcompat:1.1.0 -> 1.7.0 (*)
|    |    +--- com.dynamic.android:dynamic-api:3.0.1-rename-package-beta (*)
|    |    \--- com.github.megatronking.stringfog:xor:4.0.1 (*)
|    \--- com.github.megatronking.stringfog:xor:4.0.1 (*)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (*)
+--- org.jetbrains.kotlinx:kotlinx-serialization-json:1.7.2
|    \--- org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.7.2
|         +--- org.jetbrains.kotlin:kotlin-stdlib -> 2.0.20 (*)
|         +--- org.jetbrains.kotlinx:kotlinx-serialization-bom:1.7.2
|         |    +--- org.jetbrains.kotlinx:kotlinx-serialization-core:1.7.2 (c)
|         |    +--- org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.7.2 (c)
|         |    +--- org.jetbrains.kotlinx:kotlinx-serialization-json:1.7.2 (c)
|         |    \--- org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.2 (c)
|         +--- org.jetbrains.kotlin:kotlin-stdlib-common -> 2.0.20 (*)
|         +--- org.jetbrains.kotlinx:kotlinx-serialization-core:1.7.2
|         |    \--- org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.2
|         |         +--- org.jetbrains.kotlin:kotlin-stdlib -> 2.0.20 (*)
|         |         +--- org.jetbrains.kotlinx:kotlinx-serialization-bom:1.7.2 (*)
|         |         +--- org.jetbrains.kotlin:kotlin-stdlib-common -> 2.0.20 (*)
|         |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|         |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (c)
|         |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.20 (c)
|         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (c)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.20 (c)
+--- com.github.ChickenHook:RestrictionBypass:2.2
|    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.72 -> 2.0.20
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|    \--- androidx.appcompat:appcompat:1.1.0 -> 1.7.0 (*)
+--- dev.rikka.tools.refine:runtime:4.4.0
+--- androidx.compose:compose-bom:2024.09.01
|    +--- androidx.compose.foundation:foundation:1.7.1 (c)
|    +--- androidx.compose.material:material:1.7.1 (c)
|    +--- androidx.compose.material:material-icons-extended:1.7.1 (c)
|    +--- androidx.compose.material3:material3:1.3.0 (c)
|    +--- androidx.compose.material3:material3-window-size-class:1.3.0 (c)
|    +--- androidx.compose.runtime:runtime-livedata:1.7.1 (c)
|    +--- androidx.compose.ui:ui:1.7.1 (c)
|    +--- androidx.compose.ui:ui-text-google-fonts:1.7.1 (c)
|    +--- androidx.compose.ui:ui-tooling:1.7.1 (c)
|    +--- androidx.compose.ui:ui-tooling-preview:1.7.1 (c)
|    +--- androidx.compose.ui:ui-util:1.7.1 (c)
|    +--- androidx.compose.runtime:runtime:1.7.1 (c)
|    +--- androidx.compose.runtime:runtime-saveable:1.7.1 (c)
|    +--- androidx.compose.animation:animation:1.7.1 (c)
|    +--- androidx.compose.foundation:foundation-layout:1.7.1 (c)
|    +--- androidx.compose.foundation:foundation-android:1.7.1 (c)
|    +--- androidx.compose.material:material-android:1.7.1 (c)
|    +--- androidx.compose.material:material-icons-extended-android:1.7.1 (c)
|    +--- androidx.compose.material3:material3-android:1.3.0 (c)
|    +--- androidx.compose.material3:material3-window-size-class-android:1.3.0 (c)
|    +--- androidx.compose.ui:ui-android:1.7.1 (c)
|    +--- androidx.compose.ui:ui-text:1.7.1 (c)
|    +--- androidx.compose.ui:ui-tooling-android:1.7.1 (c)
|    +--- androidx.compose.ui:ui-tooling-preview-android:1.7.1 (c)
|    +--- androidx.compose.ui:ui-util-android:1.7.1 (c)
|    +--- androidx.compose.runtime:runtime-android:1.7.1 (c)
|    +--- androidx.compose.runtime:runtime-saveable-android:1.7.1 (c)
|    +--- androidx.compose.animation:animation-android:1.7.1 (c)
|    +--- androidx.compose.foundation:foundation-layout-android:1.7.1 (c)
|    +--- androidx.compose.animation:animation-core:1.7.1 (c)
|    +--- androidx.compose.material:material-ripple:1.7.1 (c)
|    +--- androidx.compose.material:material-icons-core:1.7.1 (c)
|    +--- androidx.compose.ui:ui-unit:1.7.1 (c)
|    +--- androidx.compose.ui:ui-geometry:1.7.1 (c)
|    +--- androidx.compose.ui:ui-graphics:1.7.1 (c)
|    +--- androidx.compose.ui:ui-text-android:1.7.1 (c)
|    +--- androidx.compose.ui:ui-tooling-data:1.7.1 (c)
|    +--- androidx.compose.animation:animation-core-android:1.7.1 (c)
|    +--- androidx.compose.material:material-ripple-android:1.7.1 (c)
|    +--- androidx.compose.material:material-icons-core-android:1.7.1 (c)
|    +--- androidx.compose.ui:ui-unit-android:1.7.1 (c)
|    +--- androidx.compose.ui:ui-geometry-android:1.7.1 (c)
|    +--- androidx.compose.ui:ui-graphics-android:1.7.1 (c)
|    \--- androidx.compose.ui:ui-tooling-data-android:1.7.1 (c)
+--- androidx.compose.ui:ui -> 1.7.1 (*)
+--- androidx.compose.ui:ui-util -> 1.7.1 (*)
+--- androidx.compose.ui:ui-tooling-preview -> 1.7.1 (*)
+--- androidx.compose.ui:ui-text-google-fonts -> 1.7.1
|    +--- androidx.compose.runtime:runtime:1.2.1 -> 1.7.1 (*)
|    +--- androidx.compose.ui:ui-text:1.7.1 (*)
|    +--- androidx.core:core:1.8.0 -> 1.13.1 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.20 (*)
|    +--- androidx.compose.ui:ui:1.7.1 (c)
|    +--- androidx.compose.ui:ui-text:1.7.1 (c)
|    +--- androidx.compose.ui:ui-tooling:1.7.1 (c)
|    +--- androidx.compose.ui:ui-tooling-preview:1.7.1 (c)
|    +--- androidx.compose.ui:ui-util:1.7.1 (c)
|    +--- androidx.compose.ui:ui-unit:1.7.1 (c)
|    +--- androidx.compose.ui:ui-geometry:1.7.1 (c)
|    +--- androidx.compose.ui:ui-graphics:1.7.1 (c)
|    \--- androidx.compose.ui:ui-tooling-data:1.7.1 (c)
+--- androidx.compose.foundation:foundation -> 1.7.1 (*)
+--- androidx.compose.material:material-icons-extended -> 1.7.1
|    \--- androidx.compose.material:material-icons-extended-android:1.7.1
|         +--- androidx.compose.material:material-icons-core:1.7.1
|         |    \--- androidx.compose.material:material-icons-core-android:1.7.1
|         |         +--- androidx.compose.ui:ui:1.6.0 -> 1.7.1 (*)
|         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.20 (*)
|         |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22 -> 2.0.20 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22 -> 2.0.20 (*)
+--- androidx.compose.material:material -> 1.7.1 (*)
+--- androidx.compose.runtime:runtime-livedata -> 1.7.1
|    +--- androidx.compose.runtime:runtime:1.7.1 (*)
|    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 -> 2.8.5 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 -> 2.8.5 (*)
|    +--- androidx.lifecycle:lifecycle-runtime-compose:2.8.3 -> 2.8.5 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.20 (*)
|    +--- androidx.compose.runtime:runtime:1.7.1 (c)
|    \--- androidx.compose.runtime:runtime-saveable:1.7.1 (c)
+--- androidx.compose.material3:material3 -> 1.3.0
|    \--- androidx.compose.material3:material3-android:1.3.0
|         +--- androidx.activity:activity-compose:1.8.2 -> 1.9.2 (*)
|         +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1 (*)
|         +--- androidx.collection:collection:1.4.0 -> 1.4.2 (*)
|         +--- androidx.compose.animation:animation-core:1.6.0 -> 1.7.1 (*)
|         +--- androidx.compose.foundation:foundation:1.7.0 -> 1.7.1 (*)
|         +--- androidx.compose.foundation:foundation-layout:1.7.0 -> 1.7.1 (*)
|         +--- androidx.compose.material:material-icons-core:1.6.0 -> 1.7.1 (*)
|         +--- androidx.compose.material:material-ripple:1.7.0 -> 1.7.1 (*)
|         +--- androidx.compose.runtime:runtime:1.7.0 -> 1.7.1 (*)
|         +--- androidx.compose.ui:ui:1.6.0 -> 1.7.1 (*)
|         +--- androidx.compose.ui:ui-text:1.6.0 -> 1.7.1 (*)
|         +--- androidx.compose.ui:ui-util:1.6.0 -> 1.7.1 (*)
|         +--- androidx.lifecycle:lifecycle-common-java8:2.6.1 -> 2.8.5
|         |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    +--- androidx.lifecycle:lifecycle-common:2.8.5 (*)
|         |    +--- androidx.lifecycle:lifecycle-common:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-process:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-runtime:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-runtime-compose:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-service:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel-compose:2.8.5 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.5 (c)
|         |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.5 (c)
|         +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22 -> 2.0.20 (*)
|         \--- androidx.compose.material3:material3-window-size-class:1.3.0 (c)
