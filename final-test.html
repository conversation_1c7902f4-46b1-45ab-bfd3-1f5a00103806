<!DOCTYPE html>
<html>
<head>
    <title>最终测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .path { background: #f0f8ff; padding: 10px; margin: 10px 0; border-left: 4px solid #007acc; }
        .target { background: #fff3cd; border-left-color: #ffc107; }
        .debug { background: #f8f9fa; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>最终测试 - 验证 com.thirdparty.android:apicmob:******* 的父引用</h1>
    <div id="output"></div>

    <script>
        // 使用实际的依赖结构片段
        const testData = `+--- project :biblecore
|    +--- androidx.databinding:viewbinding:8.4.0 -> 8.4.1 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|    +--- androidx.core:core-ktx:1.13.1 (*)
|    +--- com.android.iaa:ads:7.20.0-panglem2-adjust-admob-beta
|    |    +--- com.github.bumptech.glide:glide:4.9.0 -> 4.16.0 (*)
|    |    +--- com.google.firebase:firebase-analytics:20.1.2
|    |    +--- com.thirdparty.android:nat-admob-adapter:********.0
|    |    +--- com.applovin:applovin-sdk:13.0.1 (*)
|    |    +--- com.thirdparty.android:kwai-api:1.2.13
|    |    +--- com.thirdparty.android:kwai-impl:1.2.13
|    |    +--- com.google.android.material:material:1.12.0 (*)
|    |    +--- androidx.media3:media3-exoplayer:1.3.1 (*)
|    |    +--- com.thirdparty.android:apicmob:*******
|    |    +--- com.github.megatronking.stringfog:xor:4.0.1 (*)
|    |    \\--- com.android.iaa:basement:7.19.0-beta
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (*)
+--- androidx.core:core-ktx:1.13.1 (*)
+--- com.google.protobuf:protobuf-javalite:4.28.1`;

        class DependencyNode {
            constructor(name, version, level) {
                this.name = name;
                this.version = version;
                this.level = level;
                this.children = [];
                this.parent = null;
                this.fullName = version ? `${name}:${version}` : name;
            }

            addChild(child) {
                child.parent = this;
                this.children.push(child);
            }
        }

        function calculateLevel(line) {
            const match = line.match(/^(\s*[|\\]*\s*)[+\\\-]/);
            if (!match) return 0;
            
            const prefix = match[1];
            const pipes = (prefix.match(/\|/g) || []).length;
            
            return pipes;
        }

        function extractDependency(line) {
            const cleanLine = line.replace(/^[\s|+\\\-]*/, '');
            
            let match;
            
            // 匹配 project :name 格式
            match = cleanLine.match(/^(project\s+:[a-zA-Z0-9\.\-_]+)/);
            if (match) {
                return { name: match[1], version: '' };
            }
            
            // 匹配标准的 group:artifact:version 格式
            match = cleanLine.match(/^([a-zA-Z0-9\.\-_]+:[a-zA-Z0-9\.\-_]+)(?::([a-zA-Z0-9\.\-_\+\-]+))?/);
            if (match) {
                const name = match[1];
                let version = match[2] || '';
                
                const versionConflictMatch = cleanLine.match(/-> ([a-zA-Z0-9\.\-_\+\-]+)/);
                if (versionConflictMatch) {
                    version = versionConflictMatch[1];
                }
                
                return { name, version };
            }
            
            return null;
        }

        function parseDependencyFile(content) {
            const lines = content.split('\n');
            const root = new DependencyNode('root', '', -1);
            const stack = [root];
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                if (!line.trim()) continue;
                
                if (!line.match(/[+\\\-]/)) continue;
                
                const level = calculateLevel(line);
                const dependency = extractDependency(line);
                
                if (!dependency) continue;
                
                // 调整栈到正确的层级
                while (stack.length > level + 1) {
                    stack.pop();
                }
                
                const node = new DependencyNode(dependency.name, dependency.version, level);
                
                // 添加到父节点
                if (stack.length > 0) {
                    stack[stack.length - 1].addChild(node);
                }
                
                // 将当前节点推入栈
                stack.push(node);
            }
            
            return root;
        }

        function findDependencyPaths(root, query) {
            const paths = [];
            const normalizedQuery = query.toLowerCase();
            
            function traverse(node, currentPath) {
                const newPath = [...currentPath, node];
                
                if (node.name && (
                    node.name.toLowerCase().includes(normalizedQuery) ||
                    node.fullName.toLowerCase().includes(normalizedQuery)
                )) {
                    paths.push(newPath.slice(1)); // 排除root节点
                }
                
                for (const child of node.children) {
                    traverse(child, newPath);
                }
            }
            
            traverse(root, []);
            return paths;
        }

        function getPathString(path) {
            return path.map(node => node.fullName || node.name).join(' → ');
        }

        // 执行测试
        const tree = parseDependencyFile(testData);
        const paths = findDependencyPaths(tree, 'com.thirdparty.android:apicmob:*******');
        
        let output = '<h2>搜索结果:</h2>';
        output += `<p>搜索目标: <strong>com.thirdparty.android:apicmob:*******</strong></p>`;
        output += `<p>找到路径数: <strong>${paths.length}</strong></p>`;
        
        if (paths.length > 0) {
            output += '<h3>完整的父引用路径:</h3>';
            paths.forEach((path, index) => {
                const pathString = getPathString(path);
                output += `<div class="path target">
                    <strong>路径 ${index + 1}:</strong><br>
                    ${pathString}
                    <br><small>层级数: ${path.length}</small>
                </div>`;
            });
            
            output += '<h3>预期结果验证:</h3>';
            const expectedPath = 'project :biblecore → com.android.iaa:ads:7.20.0-panglem2-adjust-admob-beta → com.thirdparty.android:apicmob:*******';
            const actualPath = paths.length > 0 ? getPathString(paths[0]) : '未找到';
            
            output += `<div class="debug">
                <strong>预期路径:</strong> ${expectedPath}<br>
                <strong>实际路径:</strong> ${actualPath}<br>
                <strong>匹配结果:</strong> ${actualPath === expectedPath ? '✅ 匹配' : '❌ 不匹配'}
            </div>`;
        } else {
            output += '<p class="path">❌ 未找到任何路径</p>';
        }
        
        document.getElementById('output').innerHTML = output;
    </script>
</body>
</html>
