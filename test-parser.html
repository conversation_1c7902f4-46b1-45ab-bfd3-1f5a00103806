<!DOCTYPE html>
<html>
<head>
    <title>依赖解析测试</title>
</head>
<body>
    <h1>依赖解析测试</h1>
    <div id="output"></div>

    <script>
        // 测试数据 - 模拟真实的Gradle依赖树结构
        const testData = `+--- project :biblecore
|    +--- androidx.databinding:viewbinding:8.4.0 -> 8.4.1 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|    +--- androidx.core:core-ktx:1.13.1 (*)
|    +--- com.android.iaa:ads:7.20.0-panglem2-adjust-admob-beta
|    |    +--- com.github.bumptech.glide:glide:4.9.0 -> 4.16.0 (*)
|    |    +--- com.google.firebase:firebase-analytics:20.1.2
|    |    +--- com.thirdparty.android:nat-admob-adapter:********.0
|    |    +--- com.thirdparty.android:kwai-api:1.2.13
|    |    +--- com.thirdparty.android:kwai-impl:1.2.13
|    |    +--- com.google.android.material:material:1.12.0 (*)
|    |    +--- androidx.media3:media3-exoplayer:1.3.1 (*)
|    |    +--- com.thirdparty.android:apicmob:*******
|    |    +--- com.github.megatronking.stringfog:xor:4.0.1 (*)
|    |    \\--- com.android.iaa:basement:7.19.0-beta
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (*)
+--- androidx.core:core-ktx:1.13.1 (*)
+--- com.google.protobuf:protobuf-javalite:4.28.1
+--- project :iconloaderlib
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|    +--- androidx.core:core-ktx:1.13.1 (*)
|    \\--- androidx.palette:palette-ktx:1.0.0
+--- com.thirdparty.android:apicmob:*******`;

        class DependencyNode {
            constructor(name, version, level) {
                this.name = name;
                this.version = version;
                this.level = level;
                this.children = [];
                this.parent = null;
                this.fullName = version ? `${name}:${version}` : name;
            }

            addChild(child) {
                child.parent = this;
                this.children.push(child);
            }
        }

        function calculateLevel(line) {
            const match = line.match(/^(\s*[|\\]*\s*)[+\\\-]/);
            if (!match) return 0;
            
            const prefix = match[1];
            const pipes = (prefix.match(/\|/g) || []).length;
            
            return pipes;
        }

        function extractDependency(line) {
            const cleanLine = line.replace(/^[\s|+\\\-]*/, '');
            
            let match;
            
            // 匹配 project :name 格式
            match = cleanLine.match(/^(project\s+:[a-zA-Z0-9\.\-_]+)/);
            if (match) {
                return { name: match[1], version: '' };
            }
            
            // 匹配标准的 group:artifact:version 格式
            match = cleanLine.match(/^([a-zA-Z0-9\.\-_]+:[a-zA-Z0-9\.\-_]+)(?::([a-zA-Z0-9\.\-_\+\-]+))?/);
            if (match) {
                const name = match[1];
                let version = match[2] || '';
                
                const versionConflictMatch = cleanLine.match(/-> ([a-zA-Z0-9\.\-_\+\-]+)/);
                if (versionConflictMatch) {
                    version = versionConflictMatch[1];
                }
                
                return { name, version };
            }
            
            return null;
        }

        function parseDependencyFile(content) {
            const lines = content.split('\n');
            const root = new DependencyNode('root', '', -1);
            const stack = [root];
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                if (!line.trim()) continue;
                
                if (!line.match(/[+\\\-]/)) continue;
                
                const level = calculateLevel(line);
                const dependency = extractDependency(line);
                
                if (!dependency) continue;
                
                while (stack.length > level + 2) {
                    stack.pop();
                }
                
                const node = new DependencyNode(dependency.name, dependency.version, level);
                
                if (stack.length > 0) {
                    stack[stack.length - 1].addChild(node);
                }
                
                stack.push(node);
            }
            
            return root;
        }

        function findDependencyPaths(root, query) {
            const paths = [];
            const normalizedQuery = query.toLowerCase();
            
            function traverse(node, currentPath) {
                const newPath = [...currentPath, node];
                
                if (node.name && (
                    node.name.toLowerCase().includes(normalizedQuery) ||
                    node.fullName.toLowerCase().includes(normalizedQuery)
                )) {
                    paths.push(newPath.slice(1)); // 排除root节点
                }
                
                for (const child of node.children) {
                    traverse(child, newPath);
                }
            }
            
            traverse(root, []);
            return paths;
        }

        function printTree(node, indent = '') {
            let result = '';
            if (node.name !== 'root') {
                result += `${indent}${node.fullName || node.name} (level: ${node.level})\n`;
            }

            for (const child of node.children) {
                result += printTree(child, indent + '  ');
            }
            return result;
        }

        function displayPaths(paths) {
            let output = '<h2>找到的路径:</h2>';

            if (paths.length === 0) {
                output += '<p>未找到匹配的路径</p>';
                return output;
            }

            paths.forEach((path, index) => {
                output += `<h3>路径 ${index + 1}:</h3><ol>`;
                path.forEach(node => {
                    output += `<li><strong>${node.fullName || node.name}</strong> (level: ${node.level})</li>`;
                });
                output += '</ol>';
            });

            return output;
        }

        // 执行测试
        const tree = parseDependencyFile(testData);

        const treeStructure = printTree(tree);
        const paths = findDependencyPaths(tree, 'apicmob');

        // 显示结果
        let output = '<h2>解析后的树结构:</h2>';
        output += '<pre>' + treeStructure + '</pre>';
        output += displayPaths(paths);

        document.getElementById('output').innerHTML = output;
    </script>
</body>
</html>
