+--- androidx.core:core-ktx:1.13.1 (*)
+--- com.google.protobuf:protobuf-javalite:4.28.1
+--- project :iconloaderlib
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|    +--- androidx.core:core-ktx:1.13.1 (*)
|    \--- androidx.palette:palette-ktx:1.0.0
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.2.50 -> 2.0.20 (*)
|         \--- androidx.palette:palette:1.0.0
|              +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|              \--- androidx.legacy:legacy-support-core-utils:1.0.0
|                   +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|                   +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|                   +--- androidx.documentfile:documentfile:1.0.0
|                   |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|                   +--- androidx.loader:loader:1.0.0
|                   |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|                   |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|                   |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.5
|                   |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|                   |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|                   |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.5 (*)
|                   |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.5
|                   |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.5 (*)
|                   |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.20 (*)
|                   |    |    |    \--- androidx.lifecycle:lifecycle-common:2.8.5 (c)
|                   |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.20 (*)
|                   |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|                   |    |    \--- androidx.lifecycle:lifecycle-common:2.8.5 (c)
|                   |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.5 (*)
|                   +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|                   |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|                   \--- androidx.print:print:1.0.0
|                        \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
+--- project :searchuilib
|    \--- androidx.core:core-ktx:1.13.1 (*)
+--- project :animationlib
|    +--- androidx.core:core-ktx:1.13.1 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.21
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.21 -> 2.0.20 (*)
|    +--- androidx.core:core-animation:1.0.0-rc01 -> 1.0.0
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|    |    +--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    |    +--- androidx.core:core:1.13.0 -> 1.13.1 (*)
|    |    \--- androidx.tracing:tracing:1.0.0 (*)
|    \--- androidx.core:core-ktx:1.12.0 -> 1.13.1 (*)
+--- androidx.profileinstaller:profileinstaller:1.3.1 (*)
+--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    +--- androidx.collection:collection:1.0.0 -> 1.4.2 (*)
|    \--- androidx.legacy:legacy-support-core-utils:1.0.0 (*)
+--- androidx.recyclerview:recyclerview:1.3.2
|    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    +--- androidx.collection:collection:1.0.0 -> 1.4.2 (*)
|    +--- androidx.core:core:1.7.0 -> 1.13.1 (*)
|    +--- androidx.customview:customview:1.0.0 -> 1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    +--- androidx.core:core:1.3.0 -> 1.13.1 (*)
|    |    \--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    +--- androidx.customview:customview-poolingcontainer:1.0.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.1.0-beta02 (c)
+--- com.applovin.mediation:bytedance-adapter:6.3.0.4.0
|    \--- com.applovin:applovin-sdk:[13.0.0,) -> 13.0.1 (*)
+--- com.applovin.mediation:unityads-adapter:4.12.4.0
|    +--- com.applovin:applovin-sdk:[13.0.0,) -> 13.0.1 (*)
|    \--- com.unity3d.ads:unity-ads:4.12.4
|         +--- androidx.activity:activity-ktx:1.7.1 -> 1.9.2 (*)
|         +--- androidx.core:core-ktx:1.9.0 -> 1.13.1 (*)
|         +--- androidx.lifecycle:lifecycle-process:2.6.1 -> 2.8.5 (*)
|         +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 -> 2.8.5 (*)
|         +--- androidx.startup:startup-runtime:1.1.1 (*)
|         +--- androidx.webkit:webkit:1.6.1 -> 1.11.0-alpha02 (*)
|         +--- androidx.datastore:datastore:1.0.0 -> 1.1.1 (*)
|         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 -> 1.9.0 (*)
|         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10 -> 1.9.10 (*)
|         +--- com.squareup.okhttp3:okhttp:3.12.13 -> 4.12.0 (*)
|         +--- com.google.protobuf:protobuf-kotlin-lite:3.21.12
|         |    +--- com.google.protobuf:protobuf-javalite:3.21.12 -> 4.28.1
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.6.0 -> 2.0.20 (*)
|         +--- androidx.work:work-runtime-ktx:2.7.0 -> 2.9.1 (*)
|         \--- com.google.android.gms:play-services-cronet:18.0.1
|              +--- com.google.android.gms:play-services-base:18.0.1 -> 18.3.0 (*)
|              +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|              +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|              \--- org.chromium.net:cronet-api:72.3626.96
+--- com.thirdparty.android:kwai-api:1.2.13
+--- com.thirdparty.android:kwai-impl:1.2.13
+--- com.google.android.material:material:1.12.0 (*)
+--- androidx.media3:media3-exoplayer:1.3.1 (*)
+--- com.thirdparty.android:apicmob:5.4.2.6
+--- com.github.megatronking.stringfog:xor:4.0.1 (*)
+--- project :biblecore
|    +--- androidx.databinding:viewbinding:8.4.0 -> 8.4.1 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|    +--- androidx.core:core-ktx:1.13.1 (*)
|    +--- androidx.core:core-ktx:1.9.0 -> 1.13.1 (*)
|    +--- androidx.appcompat:appcompat:1.6.1 -> 1.7.0 (*)
|    +--- com.google.android.material:material:1.8.0 -> 1.12.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.1.4 (*)
|    +--- androidx.legacy:legacy-support-v4:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    +--- androidx.media:media:1.0.0 -> 1.6.0
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|    |    |    +--- androidx.collection:collection:1.1.0 -> 1.4.2 (*)
|    |    |    \--- androidx.core:core:1.6.0 -> 1.13.1 (*)
|    |    +--- androidx.legacy:legacy-support-core-utils:1.0.0 (*)
|    |    +--- androidx.legacy:legacy-support-core-ui:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    +--- androidx.legacy:legacy-support-core-utils:1.0.0 (*)
|    |    |    +--- androidx.customview:customview:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.viewpager:viewpager:1.0.0 (*)
|    |    |    +--- androidx.coordinatorlayout:coordinatorlayout:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.drawerlayout:drawerlayout:1.0.0 -> 1.1.1 (*)
|    |    |    +--- androidx.slidingpanelayout:slidingpanelayout:1.0.0 -> 1.2.0 (*)
|    |    |    +--- androidx.interpolator:interpolator:1.0.0 (*)
|    |    |    +--- androidx.swiperefreshlayout:swiperefreshlayout:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|    |    |    +--- androidx.asynclayoutinflater:asynclayoutinflater:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    \--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    \--- androidx.cursoradapter:cursoradapter:1.0.0 (*)
|    |    \--- androidx.fragment:fragment:1.0.0 -> 1.8.1 (*)
|    +--- com.android.iaa:ads:7.20.0-panglem2-adjust-admob-beta
|    |    +--- com.github.bumptech.glide:glide:4.9.0 -> 4.16.0 (*)
|    |    +--- com.google.firebase:firebase-analytics:20.1.2
|    |    +--- com.google.firebase:firebase-core:20.1.2
|    |    |    \--- com.google.firebase:firebase-analytics:20.1.2 (*)
|    |    +--- com.thirdparty.android:nat-admob-adapter:********.0
|    |    +--- com.applovin:applovin-sdk:13.0.1 (*)
|    |    +--- com.applovin.mediation:bigoads-adapter:5.1.0.0
|    |    |    \--- com.bigossp:bigo-ads:5.1.0
|    |    +--- com.applovin.mediation:bidmachine-adapter:3.0.1.2
|    |    +--- com.applovin.mediation:fyber-adapter:8.3.5.0
|    |    +--- com.applovin.mediation:inmobi-adapter:10.8.0.0
|    |    +--- com.applovin.mediation:ironsource-adapter:8.4.0.0.2
|    |    +--- com.applovin.mediation:vungle-adapter:7.4.2.1
|    |    +--- com.applovin.mediation:facebook-adapter:6.18.0.1
|    |    +--- com.applovin.mediation:mintegral-adapter:16.8.81.0
|    |    +--- com.applovin.mediation:moloco-adapter:3.6.1.1
|    |    +--- com.applovin.mediation:bytedance-adapter:6.3.0.4.0
|    |    |    \--- com.applovin:applovin-sdk:[13.0.0,) -> 13.0.1 (*)
|    |    +--- com.applovin.mediation:unityads-adapter:4.12.4.0
|    |    +--- com.applovin.mediation:pubmatic-adapter:4.2.0.1
|    |    +--- com.thirdparty.android:kwai-api:1.2.13
|    |    +--- com.thirdparty.android:kwai-impl:1.2.13
|    |    +--- com.google.android.material:material:1.12.0 (*)
|    |    +--- androidx.media3:media3-exoplayer:1.3.1 (*)
|    |    +--- com.thirdparty.android:apicmob:5.4.2.6
|    |    +--- com.github.megatronking.stringfog:xor:4.0.1 (*)
|    |    \--- com.android.iaa:basement:7.19.0-beta
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-serialization-json:1.7.2
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (*)
+--- org.jetbrains.kotlinx:kotlinx-serialization-json:1.7.2
+--- com.github.ChickenHook:RestrictionBypass:2.2
|    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.72 -> 2.0.20
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|    \--- androidx.appcompat:appcompat:1.1.0 -> 1.7.0 (*)
+--- dev.rikka.tools.refine:runtime:4.4.0
