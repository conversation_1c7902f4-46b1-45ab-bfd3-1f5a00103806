# 依赖父引用查看器

一个美观的网页应用，用于分析Gradle依赖树文件，查找并展示指定依赖的所有父引用路径。

## 功能特性

### 🔍 核心功能
- **文件解析**: 支持解析Gradle依赖树输出文件
- **智能搜索**: 支持精确匹配和模糊搜索依赖名称
- **树形展示**: 以直观的树形结构展示所有父引用路径
- **高亮显示**: 自动高亮目标依赖，便于识别
- **路径统计**: 显示找到的路径数、最大深度、唯一父依赖数等统计信息

### 🎨 界面特性
- **现代设计**: 使用Tailwind CSS构建的美观界面
- **响应式布局**: 完美支持桌面端和移动端
- **图标支持**: 集成Heroicons图标库
- **动画效果**: 流畅的展开/折叠动画
- **主题切换**: 支持明暗主题切换

### 🚀 交互功能
- **拖拽上传**: 支持拖拽文件到页面进行上传
- **示例快填**: 提供常用依赖的快速填入按钮
- **展开控制**: 支持一键展开/折叠所有节点
- **搜索高亮**: 搜索结果中高亮显示匹配文本

## 使用方法

### 1. 准备依赖文件
首先需要生成Gradle依赖树文件。在Android项目根目录执行：

```bash
# 生成依赖树文件
./gradlew app:dependencies > dependency.txt

# 或者只查看特定配置的依赖
./gradlew app:dependencies --configuration releaseRuntimeClasspath > dependency.txt
```

### 2. 打开网页应用
在浏览器中打开 `dependency-viewer.html` 文件。

### 3. 上传依赖文件
有两种方式上传文件：
- **拖拽上传**: 直接将dependency.txt文件拖拽到上传区域
- **点击上传**: 点击上传区域选择文件
- **使用默认**: 点击"使用默认文件"按钮使用内置的测试文件

### 4. 搜索依赖
在搜索框中输入要查找的依赖名称，例如：
- `com.thirdparty.android:apicmob:*******` (完整依赖名)
- `com.thirdparty.android:apicmob` (不包含版本号)
- `apicmob` (只搜索artifact名称)
- `androidx.core` (搜索group名称)

### 5. 查看结果
搜索完成后，页面会显示：
- **统计信息**: 找到的路径数、最大深度、唯一父依赖数
- **树形结构**: 所有包含目标依赖的路径，以树形结构展示
- **高亮显示**: 目标依赖会以黄色背景高亮显示

## 文件结构

```
dependency-viewer/
├── dependency-viewer.html    # 主应用文件
├── test-dependency.txt      # 测试用依赖文件
├── dependency.txt          # 您的实际依赖文件
└── README.md              # 说明文档
```

## 技术实现

### 前端技术栈
- **HTML5**: 页面结构
- **Tailwind CSS**: 样式框架
- **JavaScript ES6+**: 核心逻辑
- **Heroicons**: 图标库

### 核心算法
1. **依赖解析算法**: 解析Gradle依赖树的层级结构
2. **路径查找算法**: 查找从根节点到目标依赖的所有路径
3. **树形构建算法**: 构建可交互的树形展示结构

### 数据结构
```javascript
// 依赖节点结构
{
  name: "com.example:library",     // 依赖名称
  version: "1.0.0",               // 版本号
  level: 2,                       // 层级深度
  children: [],                   // 子依赖数组
  parent: parentNode,             // 父节点引用
  fullName: "com.example:library:1.0.0"  // 完整名称
}
```

## 使用示例

### 示例1: 查找特定依赖
搜索 `com.thirdparty.android:apicmob:*******`，系统会显示所有引用这个依赖的路径。

### 示例2: 查找androidx依赖
搜索 `androidx.core`，会显示所有androidx.core相关的依赖及其父引用。

### 示例3: 分析依赖冲突
当存在版本冲突时（如 `1.0.0 -> 2.0.0`），可以通过查看父引用路径来分析冲突来源。

## 注意事项

1. **文件格式**: 确保上传的是标准的Gradle依赖树输出文件
2. **文件大小**: 大型项目的依赖文件可能较大，解析需要一些时间
3. **浏览器兼容**: 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
4. **本地运行**: 由于浏览器安全限制，建议通过HTTP服务器运行，而不是直接打开文件

## 故障排除

### 常见问题

**Q: 上传文件后没有反应？**
A: 检查文件格式是否正确，确保是Gradle依赖树的输出文件。

**Q: 搜索不到依赖？**
A: 确认依赖名称拼写正确，可以尝试只搜索部分名称。

**Q: 页面显示异常？**
A: 刷新页面重试，或检查浏览器控制台是否有错误信息。

**Q: 无法加载默认文件？**
A: 确保test-dependency.txt文件与HTML文件在同一目录下。

## 开发信息

- **开发语言**: HTML + CSS + JavaScript
- **样式框架**: Tailwind CSS
- **图标库**: Heroicons
- **兼容性**: 现代浏览器
- **许可证**: MIT

## 更新日志

### v1.0.0 (2024-07-23)
- ✨ 初始版本发布
- 🔍 支持依赖文件解析和搜索
- 🌳 树形结构展示
- 🎨 美观的用户界面
- 📊 统计信息展示
- 🔄 展开/折叠功能
- 📱 响应式设计

---

如有问题或建议，欢迎反馈！
