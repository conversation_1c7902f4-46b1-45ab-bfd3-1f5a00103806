<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>依赖父引用查看器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-down': 'slideDown 0.3s ease-out',
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .tree-line {
            border-left: 2px solid #e5e7eb;
            margin-left: 12px;
            padding-left: 16px;
        }
        .tree-node {
            position: relative;
        }
        .tree-node::before {
            content: '';
            position: absolute;
            left: -14px;
            top: 12px;
            width: 12px;
            height: 2px;
            background-color: #e5e7eb;
        }
        .highlight {
            background: linear-gradient(90deg, #fef3c7, #fde68a);
            border-left: 4px solid #f59e0b;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                        <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        依赖父引用查看器
                    </h1>
                    <p class="text-gray-600 mt-2">分析Gradle依赖树，查找指定依赖的所有父引用路径</p>
                </div>
                <button id="themeToggle" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Upload Section -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                上传依赖文件
            </h2>
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors" id="dropZone">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <div class="mt-4">
                    <label for="fileInput" class="cursor-pointer">
                        <span class="mt-2 block text-sm font-medium text-gray-900">
                            点击上传或拖拽dependency.txt文件到此处
                        </span>
                        <span class="mt-1 block text-sm text-gray-500">
                            支持Gradle依赖树输出文件
                        </span>
                    </label>
                    <input type="file" id="fileInput" class="hidden" accept=".txt">
                </div>
                <button id="useDefaultFile" class="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    使用默认文件
                </button>
            </div>
        </div>

        <!-- Search Section -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-8" id="searchSection" style="display: none;">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                搜索依赖
            </h2>
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" id="searchInput" placeholder="输入依赖名称，如：com.thirdparty.android:apicmob:5.4.2.6" 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <button id="searchBtn" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    搜索
                </button>
            </div>
            <div class="mt-4 flex flex-wrap gap-2">
                <button class="example-btn px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200" data-example="com.thirdparty.android:apicmob">
                    示例: apicmob
                </button>
                <button class="example-btn px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200" data-example="androidx.core:core">
                    示例: androidx.core
                </button>
                <button class="example-btn px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200" data-example="org.jetbrains.kotlin:kotlin-stdlib">
                    示例: kotlin-stdlib
                </button>
            </div>
        </div>

        <!-- Loading -->
        <div id="loading" class="hidden text-center py-8">
            <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-blue-600 bg-blue-100">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                正在解析依赖文件...
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="hidden">
            <!-- Stats -->
            <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">搜索结果统计</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600" id="totalPaths">0</div>
                        <div class="text-sm text-gray-600">找到的路径数</div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600" id="maxDepth">0</div>
                        <div class="text-sm text-gray-600">最大深度</div>
                    </div>
                    <div class="text-center p-4 bg-purple-50 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600" id="uniqueParents">0</div>
                        <div class="text-sm text-gray-600">唯一父依赖数</div>
                    </div>
                </div>
            </div>

            <!-- Tree View -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">依赖树结构</h3>
                    <div class="flex gap-2">
                        <button id="expandAll" class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                            全部展开
                        </button>
                        <button id="collapseAll" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                            全部折叠
                        </button>
                    </div>
                </div>
                <div id="treeContainer" class="max-h-96 overflow-y-auto border rounded-lg p-4 bg-gray-50">
                    <!-- Tree content will be inserted here -->
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div id="errorMessage" class="hidden bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <h3 class="text-sm font-medium text-red-800">错误</h3>
                    <div class="mt-1 text-sm text-red-700" id="errorText"></div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 全局变量
        let dependencyData = null;
        let parsedTree = null;

        // DOM元素
        const fileInput = document.getElementById('fileInput');
        const dropZone = document.getElementById('dropZone');
        const useDefaultFileBtn = document.getElementById('useDefaultFile');
        const searchSection = document.getElementById('searchSection');
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        const loading = document.getElementById('loading');
        const resultsSection = document.getElementById('resultsSection');
        const errorMessage = document.getElementById('errorMessage');
        const treeContainer = document.getElementById('treeContainer');
        const themeToggle = document.getElementById('themeToggle');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 文件上传
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽上传
            dropZone.addEventListener('dragover', handleDragOver);
            dropZone.addEventListener('drop', handleDrop);

            // 使用默认文件
            useDefaultFileBtn.addEventListener('click', loadDefaultFile);

            // 搜索功能
            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') performSearch();
            });

            // 示例按钮
            document.querySelectorAll('.example-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    searchInput.value = this.dataset.example;
                });
            });

            // 主题切换
            themeToggle.addEventListener('click', toggleTheme);

            // 树操作按钮
            document.getElementById('expandAll')?.addEventListener('click', expandAllNodes);
            document.getElementById('collapseAll')?.addEventListener('click', collapseAllNodes);
        }

        // 文件处理函数
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                readFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            dropZone.classList.add('border-blue-400', 'bg-blue-50');
        }

        function handleDrop(event) {
            event.preventDefault();
            dropZone.classList.remove('border-blue-400', 'bg-blue-50');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                readFile(files[0]);
            }
        }

        function readFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                dependencyData = e.target.result;
                parseDependencyFile(dependencyData);
            };
            reader.readAsText(file);
        }

        function loadDefaultFile() {
            // 使用测试依赖文件
            fetch('./test-dependency.txt')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('文件不存在或无法访问');
                    }
                    return response.text();
                })
                .then(data => {
                    dependencyData = data;
                    parseDependencyFile(dependencyData);
                })
                .catch(error => {
                    showError('无法加载默认文件: ' + error.message + '。请手动上传dependency.txt文件。');
                });
        }

        // 依赖解析类
        class DependencyNode {
            constructor(name, version, level) {
                this.name = name;
                this.version = version;
                this.level = level;
                this.children = [];
                this.parent = null;
                this.fullName = version ? `${name}:${version}` : name;
            }

            addChild(child) {
                child.parent = this;
                this.children.push(child);
            }
        }

        // 解析依赖文件
        function parseDependencyFile(content) {
            showLoading(true);
            hideError();

            try {
                const lines = content.split('\n');
                const root = new DependencyNode('root', '', 0);
                const stack = [root];

                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    if (!line.trim()) continue;

                    const level = calculateLevel(line);
                    const dependency = extractDependency(line);

                    if (!dependency) continue;

                    // 调整栈到正确的层级
                    while (stack.length > level + 1) {
                        stack.pop();
                    }

                    const node = new DependencyNode(dependency.name, dependency.version, level);

                    if (stack.length > 0) {
                        stack[stack.length - 1].addChild(node);
                    }

                    stack.push(node);
                }

                parsedTree = root;
                searchSection.style.display = 'block';
                showLoading(false);

            } catch (error) {
                showError('解析文件时出错: ' + error.message);
                showLoading(false);
            }
        }

        // 计算依赖层级
        function calculateLevel(line) {
            const match = line.match(/^(\s*[|\\+\-\s]*)/);
            if (!match) return 0;

            const prefix = match[1];
            // 计算实际的缩进层级
            const pipes = (prefix.match(/\|/g) || []).length;
            const plusMinus = (prefix.match(/[+\\]/g) || []).length;

            return pipes + (plusMinus > 0 ? 1 : 0);
        }

        // 提取依赖信息
        function extractDependency(line) {
            // 匹配依赖格式: group:artifact:version
            const depRegex = /([a-zA-Z0-9\.\-_]+:[a-zA-Z0-9\.\-_]+)(?::([a-zA-Z0-9\.\-_]+))?/;
            const match = line.match(depRegex);

            if (!match) return null;

            const name = match[1];
            const version = match[2] || '';

            return { name, version };
        }

        // 搜索功能
        function performSearch() {
            const query = searchInput.value.trim();
            if (!query || !parsedTree) {
                showError('请输入搜索内容并确保已加载依赖文件');
                return;
            }

            showLoading(true);
            hideError();

            setTimeout(() => {
                try {
                    const results = findDependencyPaths(parsedTree, query);
                    displayResults(results, query);
                    showLoading(false);
                } catch (error) {
                    showError('搜索时出错: ' + error.message);
                    showLoading(false);
                }
            }, 100);
        }

        // 查找依赖路径
        function findDependencyPaths(root, query) {
            const paths = [];
            const normalizedQuery = query.toLowerCase();

            function traverse(node, currentPath) {
                const newPath = [...currentPath, node];

                // 检查当前节点是否匹配
                if (node.name && (
                    node.name.toLowerCase().includes(normalizedQuery) ||
                    node.fullName.toLowerCase().includes(normalizedQuery)
                )) {
                    paths.push(newPath.slice(1)); // 排除root节点
                }

                // 递归遍历子节点
                for (const child of node.children) {
                    traverse(child, newPath);
                }
            }

            traverse(root, []);
            return paths;
        }

        // 显示结果
        function displayResults(paths, query) {
            if (paths.length === 0) {
                showError(`未找到匹配 "${query}" 的依赖`);
                return;
            }

            // 更新统计信息
            updateStats(paths);

            // 构建并显示树
            buildTreeView(paths, query);

            resultsSection.classList.remove('hidden');
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        // 更新统计信息
        function updateStats(paths) {
            const totalPaths = paths.length;
            const maxDepth = Math.max(...paths.map(path => path.length));
            const uniqueParents = new Set();

            paths.forEach(path => {
                path.forEach((node, index) => {
                    if (index < path.length - 1) { // 不包括最后一个节点（目标依赖）
                        uniqueParents.add(node.name);
                    }
                });
            });

            document.getElementById('totalPaths').textContent = totalPaths;
            document.getElementById('maxDepth').textContent = maxDepth;
            document.getElementById('uniqueParents').textContent = uniqueParents.size;
        }

        // 构建树视图
        function buildTreeView(paths, query) {
            const container = document.getElementById('treeContainer');
            container.innerHTML = '';

            // 构建树结构
            const treeRoot = {};

            paths.forEach((path, pathIndex) => {
                let current = treeRoot;

                path.forEach((node, nodeIndex) => {
                    const key = node.fullName || node.name;

                    if (!current[key]) {
                        current[key] = {
                            node: node,
                            children: {},
                            isTarget: false,
                            pathIndices: []
                        };
                    }

                    current[key].pathIndices.push(pathIndex);

                    // 检查是否为目标节点
                    if (nodeIndex === path.length - 1) {
                        current[key].isTarget = true;
                    }

                    current = current[key].children;
                });
            });

            // 渲染树
            renderTreeNode(container, treeRoot, query, 0);
        }

        // 渲染树节点
        function renderTreeNode(container, treeData, query, level) {
            Object.keys(treeData).forEach(key => {
                const item = treeData[key];
                const node = item.node;

                const nodeDiv = document.createElement('div');
                nodeDiv.className = `tree-node mb-2 ${level > 0 ? 'tree-line' : ''}`;

                const isTarget = item.isTarget;
                const hasChildren = Object.keys(item.children).length > 0;

                nodeDiv.innerHTML = `
                    <div class="flex items-center p-2 rounded ${isTarget ? 'highlight' : 'hover:bg-gray-100'} cursor-pointer"
                         onclick="toggleNode(this)">
                        ${hasChildren ? `
                            <svg class="w-4 h-4 mr-2 transform transition-transform expand-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        ` : '<div class="w-6"></div>'}

                        <div class="flex items-center">
                            ${isTarget ? `
                                <svg class="w-4 h-4 mr-2 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            ` : ''}

                            <span class="font-mono text-sm ${isTarget ? 'font-bold text-yellow-800' : 'text-gray-700'}">
                                ${highlightText(node.fullName || node.name, query)}
                            </span>

                            <span class="ml-2 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                ${item.pathIndices.length} 路径
                            </span>
                        </div>
                    </div>

                    <div class="children ml-6 hidden">
                        <!-- 子节点将在这里渲染 -->
                    </div>
                `;

                container.appendChild(nodeDiv);

                // 渲染子节点
                if (hasChildren) {
                    const childrenContainer = nodeDiv.querySelector('.children');
                    renderTreeNode(childrenContainer, item.children, query, level + 1);
                }
            });
        }

        // 高亮文本
        function highlightText(text, query) {
            if (!query) return text;

            const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
            return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
        }

        // 切换节点展开/折叠
        function toggleNode(element) {
            const childrenDiv = element.parentNode.querySelector('.children');
            const icon = element.querySelector('.expand-icon');

            if (childrenDiv) {
                childrenDiv.classList.toggle('hidden');
                if (icon) {
                    icon.classList.toggle('rotate-90');
                }
            }
        }

        // 展开所有节点
        function expandAllNodes() {
            document.querySelectorAll('.children').forEach(child => {
                child.classList.remove('hidden');
            });
            document.querySelectorAll('.expand-icon').forEach(icon => {
                icon.classList.add('rotate-90');
            });
        }

        // 折叠所有节点
        function collapseAllNodes() {
            document.querySelectorAll('.children').forEach(child => {
                child.classList.add('hidden');
            });
            document.querySelectorAll('.expand-icon').forEach(icon => {
                icon.classList.remove('rotate-90');
            });
        }

        // 主题切换
        function toggleTheme() {
            document.body.classList.toggle('dark');
            // 这里可以添加更多的暗色主题样式
        }

        // 工具函数
        function showLoading(show) {
            loading.classList.toggle('hidden', !show);
        }

        function showError(message) {
            document.getElementById('errorText').textContent = message;
            errorMessage.classList.remove('hidden');
        }

        function hideError() {
            errorMessage.classList.add('hidden');
        }
    </script>
</body>
</html>
