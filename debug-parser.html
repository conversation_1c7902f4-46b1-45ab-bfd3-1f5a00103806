<!DOCTYPE html>
<html>
<head>
    <title>依赖解析调试</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        .path { background: #e8f5e8; padding: 5px; margin: 5px 0; }
        .error { background: #ffe8e8; padding: 5px; margin: 5px 0; }
    </style>
</head>
<body>
    <h1>依赖解析调试工具</h1>
    <div id="output"></div>

    <script>
        // 测试数据 - 简化的真实结构
        const testData = `+--- project :biblecore
|    +--- androidx.databinding:viewbinding:8.4.0 -> 8.4.1 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.20 (*)
|    +--- com.android.iaa:ads:7.20.0-panglem2-adjust-admob-beta
|    |    +--- com.github.bumptech.glide:glide:4.9.0 -> 4.16.0 (*)
|    |    +--- com.google.firebase:firebase-analytics:20.1.2
|    |    +--- com.thirdparty.android:apicmob:*******
|    |    +--- com.github.megatronking.stringfog:xor:4.0.1 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (*)
+--- androidx.core:core-ktx:1.13.1 (*)
+--- com.google.protobuf:protobuf-javalite:4.28.1`;

        class DependencyNode {
            constructor(name, version, level) {
                this.name = name;
                this.version = version;
                this.level = level;
                this.children = [];
                this.parent = null;
                this.fullName = version ? `${name}:${version}` : name;
            }

            addChild(child) {
                child.parent = this;
                this.children.push(child);
            }
        }

        function calculateLevel(line) {
            const match = line.match(/^(\s*[|\\]*\s*)[+\\\-]/);
            if (!match) return 0;
            
            const prefix = match[1];
            const pipes = (prefix.match(/\|/g) || []).length;
            
            return pipes;
        }

        function extractDependency(line) {
            const cleanLine = line.replace(/^[\s|+\\\-]*/, '');
            
            let match;
            
            // 匹配 project :name 格式
            match = cleanLine.match(/^(project\s+:[a-zA-Z0-9\.\-_]+)/);
            if (match) {
                return { name: match[1], version: '' };
            }
            
            // 匹配标准的 group:artifact:version 格式
            match = cleanLine.match(/^([a-zA-Z0-9\.\-_]+:[a-zA-Z0-9\.\-_]+)(?::([a-zA-Z0-9\.\-_\+\-]+))?/);
            if (match) {
                const name = match[1];
                let version = match[2] || '';
                
                const versionConflictMatch = cleanLine.match(/-> ([a-zA-Z0-9\.\-_\+\-]+)/);
                if (versionConflictMatch) {
                    version = versionConflictMatch[1];
                }
                
                return { name, version };
            }
            
            return null;
        }

        function parseDependencyFile(content) {
            const lines = content.split('\n');
            const root = new DependencyNode('root', '', -1);
            const stack = [root];
            let debugInfo = [];
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                if (!line.trim()) continue;
                
                if (!line.match(/[+\\\-]/)) continue;
                
                const level = calculateLevel(line);
                const dependency = extractDependency(line);
                
                if (!dependency) continue;
                
                debugInfo.push(`Line ${i+1}: "${line}"`);
                debugInfo.push(`  Level: ${level}, Dependency: ${dependency.name}:${dependency.version}`);
                debugInfo.push(`  Stack before: [${stack.map(n => n.name).join(', ')}]`);
                
                // 调整栈到正确的层级
                while (stack.length > level + 1) {
                    stack.pop();
                }
                
                debugInfo.push(`  Stack after pop: [${stack.map(n => n.name).join(', ')}]`);
                
                const node = new DependencyNode(dependency.name, dependency.version, level);
                
                // 添加到父节点
                if (stack.length > 0) {
                    stack[stack.length - 1].addChild(node);
                    debugInfo.push(`  Added to parent: ${stack[stack.length - 1].name}`);
                }
                
                // 将当前节点推入栈
                stack.push(node);
                debugInfo.push(`  Stack final: [${stack.map(n => n.name).join(', ')}]`);
                debugInfo.push('');
            }
            
            return { tree: root, debug: debugInfo };
        }

        function findDependencyPaths(root, query) {
            const paths = [];
            const normalizedQuery = query.toLowerCase();
            
            function traverse(node, currentPath) {
                const newPath = [...currentPath, node];
                
                if (node.name && (
                    node.name.toLowerCase().includes(normalizedQuery) ||
                    node.fullName.toLowerCase().includes(normalizedQuery)
                )) {
                    paths.push(newPath.slice(1)); // 排除root节点
                }
                
                for (const child of node.children) {
                    traverse(child, newPath);
                }
            }
            
            traverse(root, []);
            return paths;
        }

        function printTree(node, indent = '') {
            let result = '';
            if (node.name !== 'root') {
                result += `${indent}${node.fullName || node.name} (level: ${node.level})\n`;
            }
            
            for (const child of node.children) {
                result += printTree(child, indent + '  ');
            }
            return result;
        }

        function displayPaths(paths) {
            let output = '<h2>找到的路径:</h2>';
            
            if (paths.length === 0) {
                output += '<p class="error">未找到匹配的路径</p>';
                return output;
            }
            
            paths.forEach((path, index) => {
                output += `<div class="path"><h3>路径 ${index + 1}:</h3><ol>`;
                path.forEach(node => {
                    output += `<li><strong>${node.fullName || node.name}</strong> (level: ${node.level})</li>`;
                });
                output += '</ol></div>';
            });
            
            return output;
        }

        // 执行测试
        const result = parseDependencyFile(testData);
        const tree = result.tree;
        const debugInfo = result.debug;
        
        const treeStructure = printTree(tree);
        const paths = findDependencyPaths(tree, 'apicmob');
        
        // 显示结果
        let output = '<h2>调试信息:</h2>';
        output += '<div class="debug"><pre>' + debugInfo.join('\n') + '</pre></div>';
        
        output += '<h2>解析后的树结构:</h2>';
        output += '<div class="debug"><pre>' + treeStructure + '</pre></div>';
        
        output += displayPaths(paths);
        
        document.getElementById('output').innerHTML = output;
    </script>
</body>
</html>
